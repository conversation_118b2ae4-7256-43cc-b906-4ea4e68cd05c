const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Configuration file path
const CONFIG_FILE = path.join(__dirname, '../../config/team-request-config.json');

// Default configuration
const defaultConfig = {
    categoryName: 'Team Requests',
    staffRoleNames: ['Admin', 'Moderator', 'Staff'],
    logChannelName: 'team-logs'
};

// Load configuration
function loadConfig() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            const data = fs.readFileSync(CONFIG_FILE, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Error loading team request config:', error);
    }
    return { ...defaultConfig };
}

// Save configuration
function saveConfig(config) {
    try {
        const configDir = path.dirname(CONFIG_FILE);
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving team request config:', error);
        return false;
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('configure-team-roles')
        .setDescription('Configure roles that can accept and close team request tickets')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Add a role to team request staff')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to add to team request staff')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a role from team request staff')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role to remove from team request staff')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all configured team request staff roles'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('clear')
                .setDescription('Clear all configured team request staff roles'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset to default configuration')),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();
        const config = loadConfig();

        switch (subcommand) {
            case 'add':
                const roleToAdd = interaction.options.getRole('role');
                
                if (config.staffRoleNames.includes(roleToAdd.name)) {
                    const embed = new EmbedBuilder()
                        .setColor('#ff9900')
                        .setTitle('⚠️ Role Already Configured')
                        .setDescription(`The role **${roleToAdd.name}** is already in the team request staff list.`)
                        .setTimestamp();
                    
                    return await interaction.reply({ embeds: [embed], ephemeral: true });
                }

                config.staffRoleNames.push(roleToAdd.name);
                
                if (saveConfig(config)) {
                    const embed = new EmbedBuilder()
                        .setColor('#2ecc71')
                        .setTitle('✅ Role Added Successfully')
                        .setDescription(`**${roleToAdd.name}** has been added to team request staff roles.\n\nThis role can now accept and close team request tickets.`)
                        .addFields({
                            name: 'Current Staff Roles',
                            value: config.staffRoleNames.map(role => `• ${role}`).join('\n') || 'None',
                            inline: false
                        })
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to save configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;

            case 'remove':
                const roleToRemove = interaction.options.getRole('role');
                
                if (!config.staffRoleNames.includes(roleToRemove.name)) {
                    const embed = new EmbedBuilder()
                        .setColor('#ff9900')
                        .setTitle('⚠️ Role Not Found')
                        .setDescription(`The role **${roleToRemove.name}** is not in the team request staff list.`)
                        .setTimestamp();
                    
                    return await interaction.reply({ embeds: [embed], ephemeral: true });
                }

                config.staffRoleNames = config.staffRoleNames.filter(role => role !== roleToRemove.name);
                
                if (saveConfig(config)) {
                    const embed = new EmbedBuilder()
                        .setColor('#e74c3c')
                        .setTitle('✅ Role Removed Successfully')
                        .setDescription(`**${roleToRemove.name}** has been removed from team request staff roles.\n\nThis role can no longer accept or close team request tickets.`)
                        .addFields({
                            name: 'Current Staff Roles',
                            value: config.staffRoleNames.map(role => `• ${role}`).join('\n') || 'None',
                            inline: false
                        })
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to save configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;

            case 'list':
                const embed = new EmbedBuilder()
                    .setColor('#3498db')
                    .setTitle('🛡️ Team Request Staff Roles')
                    .setDescription('These roles can accept and close team request tickets:')
                    .addFields({
                        name: 'Configured Staff Roles',
                        value: config.staffRoleNames.map(role => `• ${role}`).join('\n') || 'None configured',
                        inline: false
                    })
                    .addFields({
                        name: 'Additional Permissions',
                        value: '• Users with **Administrator** permission can always manage tickets\n• Role names are matched using partial matching (case-insensitive)',
                        inline: false
                    })
                    .setFooter({ text: 'Use /configure-team-roles add/remove to modify this list' })
                    .setTimestamp();
                
                await interaction.reply({ embeds: [embed], ephemeral: true });
                break;

            case 'clear':
                config.staffRoleNames = [];
                
                if (saveConfig(config)) {
                    const embed = new EmbedBuilder()
                        .setColor('#e74c3c')
                        .setTitle('🗑️ All Roles Cleared')
                        .setDescription('All team request staff roles have been cleared.\n\n⚠️ **Warning:** Only users with Administrator permission will be able to manage team request tickets now.')
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to save configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;

            case 'reset':
                const resetConfig = { ...defaultConfig };
                
                if (saveConfig(resetConfig)) {
                    const embed = new EmbedBuilder()
                        .setColor('#9b59b6')
                        .setTitle('🔄 Configuration Reset')
                        .setDescription('Team request configuration has been reset to default values.')
                        .addFields({
                            name: 'Default Staff Roles',
                            value: resetConfig.staffRoleNames.map(role => `• ${role}`).join('\n'),
                            inline: false
                        })
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to reset configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;
        }
    },
};
