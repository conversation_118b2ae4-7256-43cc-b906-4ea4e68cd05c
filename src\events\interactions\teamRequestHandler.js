const { EmbedBuilder, ChannelType, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');
const fs = require('fs');
const path = require('path');

// Configuration file path
const CONFIG_FILE = path.join(__dirname, '../../config/team-request-config.json');

// Default team request ticket configuration
const DEFAULT_TEAM_REQUEST_CONFIG = {
    categoryName: 'Team Requests',
    staffRoleNames: ['Admin', 'Moderator', 'Staff'], // Add your staff role names here
    logChannelName: 'team-logs' // Optional: channel to log team actions
};

// Load configuration
function loadTeamRequestConfig() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            const data = fs.readFileSync(CONFIG_FILE, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Error loading team request config:', error);
    }
    return { ...DEFAULT_TEAM_REQUEST_CONFIG };
}

async function createTeamTicket(interaction, requestType) {
    try {
        const guild = interaction.guild;
        const user = interaction.user;
        const config = loadTeamRequestConfig();

        // Find or create category
        let category = guild.channels.cache.find(c =>
            c.type === ChannelType.GuildCategory &&
            c.name === config.categoryName
        );
        
        if (!category) {
            category = await guild.channels.create({
                name: config.categoryName,
                type: ChannelType.GuildCategory,
                permissionOverwrites: [
                    {
                        id: guild.roles.everyone,
                        deny: [PermissionFlagsBits.ViewChannel]
                    }
                ]
            });
        }

        // Find staff roles
        const staffRoles = guild.roles.cache.filter(role =>
            config.staffRoleNames.some(staffRoleName =>
                role.name.toLowerCase().includes(staffRoleName.toLowerCase())
            )
        );

        // Create permission overwrites
        const permissionOverwrites = [
            {
                id: guild.roles.everyone,
                deny: [PermissionFlagsBits.ViewChannel]
            },
            {
                id: user.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory
                ]
            }
        ];

        // Add staff roles to permissions
        staffRoles.forEach(role => {
            permissionOverwrites.push({
                id: role.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory,
                    PermissionFlagsBits.ManageMessages
                ]
            });
        });

        // Create ticket channel
        const ticketChannel = await guild.channels.create({
            name: `${requestType}-${user.username}`,
            type: ChannelType.GuildText,
            parent: category.id,
            permissionOverwrites: permissionOverwrites
        });

        // Get request type details
        const requestDetails = getRequestTypeDetails(requestType);
        
        // Get user's current team info
        const teamManager = getTeamManager();
        const memberInfo = await teamManager.getMemberInfo(user.id);

        // Create ticket embed
        const ticketEmbed = new EmbedBuilder()
            .setColor(requestDetails.color)
            .setTitle(`${requestDetails.emoji} ${requestDetails.title}`)
            .setDescription(`**User:** ${user}\n**Request Type:** ${requestDetails.title}\n\n${requestDetails.description}`)
            .addFields(
                { name: '👤 Current Status', value: memberInfo.team_id ? `In team: **${memberInfo.team_name}**` : 'Not in any team', inline: true },
                { name: '🏆 Points', value: `${memberInfo.points}`, inline: true },
                { name: '⭐ Level', value: `${memberInfo.level}`, inline: true }
            )
            .setFooter({ text: `Ticket created at` })
            .setTimestamp();

        // Create close button
        const closeButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`close_team_ticket_${ticketChannel.id}`)
                    .setLabel('🔒 Close Ticket')
                    .setStyle(ButtonStyle.Danger)
            );

        // Send initial message
        const initialMessage = await ticketChannel.send({
            content: `${user} ${staffRoles.size > 0 ? staffRoles.map(r => `<@&${r.id}>`).join(' ') : '@Staff'}`,
            embeds: [ticketEmbed],
            components: [closeButton]
        });

        // Pin the initial message
        await initialMessage.pin();

        // Send instructions for staff - REMOVED AS REQUESTED
        /*
        const instructionsEmbed = new EmbedBuilder()
            .setColor('#747595')
            .setTitle('📋 Staff Instructions')
            .setDescription('استخدم الأوامر التالية لمعالجة هذا الطلب')
            .addFields(
                { name: '🆕 إنشاء فريق', value: '`/create-team team_name:<name>`', inline: false },
                { name: '📥 إضافة إلى فريق', value: '`/add-to-team member:@user team_name:<name>`', inline: false },
                { name: '📤 إزالة من الفريق', value: '`/remove-from-team member:@user`', inline: false },
                { name: '📊 التحقق من المعلومات', value: '`/team-info member:@user` or `/team-info team:<name>`', inline: false },
                { name: '❓ لطلبات المساعدة', value: 'Answer the user\'s question directly in this ticket', inline: false }
            )
            .setFooter({ text: 'أغلق هذه التذكرة عند الانتهاء من معالجة الطلب' });
        */

        // Staff instructions message removed as requested
        // await ticketChannel.send({ embeds: [instructionsEmbed] });

        // Log the ticket creation
        await logTeamAction(guild, 'Ticket Created', `${requestDetails.title} ticket created by ${user.tag}`, user, requestDetails.color);

        return ticketChannel;

    } catch (error) {
        console.error('Error creating team ticket:', error);
        throw error;
    }
}

function getRequestTypeDetails(requestType) {
    const details = {
        'leave': {
            title: 'Leave Team Request',
            emoji: '📤',
            color: '#FFA500',
            description: 'يرجى تأكيد رغبتك في مغادرة فريقك الحالي، وذكر السبب إذا رغبت في ذلك'
        },
        'create': {
            title: 'Create Team Request',
            emoji: '🆕',
            color: '#00FF00',
            description: 'يرجى تزويدنا باسم فريقك الجديد'
        },
        'help': {
            title: 'Help & Inquiry Request',
            emoji: '❓',
            color: '#FFA500',
            description: 'يرجى وصف سؤالك أو ما تحتاج المساعدة بشأنه في نظام الفرق.'
        }
    };

    return details[requestType] || details['leave'];
}

async function logTeamAction(guild, action, description, user, color = '#0099FF') {
    try {
        const logChannel = guild.channels.cache.find(c => c.name === TEAM_REQUEST_CONFIG.logChannelName);
        if (!logChannel) return;

        const logEmbed = new EmbedBuilder()
            .setColor(color)
            .setTitle(`🏅 Team System - ${action}`)
            .setDescription(description)
            .addFields(
                { name: '👤 User', value: `${user.tag} (${user.id})`, inline: true },
                { name: '📅 Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            )
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    } catch (error) {
        console.error('Error logging team action:', error);
    }
}

async function closeTeamTicket(interaction) {
    try {
        const channel = interaction.channel;
        const user = interaction.user;
        const config = loadTeamRequestConfig();

        // Check if user has permission to close tickets
        // Allow users with Administrator permissions or configured staff roles
        const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);

        const hasStaffRole = interaction.member.roles.cache.some(role =>
            config.staffRoleNames.some(staffRoleName =>
                role.name.toLowerCase().includes(staffRoleName.toLowerCase())
            )
        );

        const hasPermission = hasAdminPermission || hasStaffRole;

        if (!hasPermission) {
            await interaction.reply({
                content: '❌ You do not have permission to close team request tickets. Only administrators and staff members can close tickets.',
                ephemeral: true
            });
            return;
        }

        // Get the ticket creator ID from channel name
        const channelNameParts = channel.name.split('-');
        const username = channelNameParts.length > 1 ? channelNameParts.slice(1).join('-') : 'unknown';

        // Remove the ticket creator's permissions (they can no longer see the channel)
        const ticketCreatorId = channel.permissionOverwrites.cache.find(overwrite =>
            overwrite.type === 1 && // User type
            overwrite.allow.has(PermissionFlagsBits.ViewChannel) &&
            overwrite.id !== interaction.guild.roles.everyone.id
        )?.id;

        if (ticketCreatorId) {
            await channel.permissionOverwrites.edit(ticketCreatorId, {
                ViewChannel: false,
                SendMessages: false
            });
        }

        // Rename the channel to closed-username
        await channel.setName(`closed-${username}`);

        // Find or create "Closed Tickets" category
        let closedCategory = interaction.guild.channels.cache.find(
            ch => ch.type === ChannelType.GuildCategory && ch.name.toLowerCase() === 'closed tickets'
        );

        if (!closedCategory) {
            closedCategory = await interaction.guild.channels.create({
                name: 'Closed Tickets',
                type: ChannelType.GuildCategory,
            });
        }

        // Move the channel to the closed tickets category
        await channel.setParent(closedCategory);

        // Create closing embed
        const closingEmbed = new EmbedBuilder()
            .setColor('#747595')
            .setTitle('🔒 Team Ticket Closed')
            .setDescription('This team ticket has been closed and archived. The ticket creator has been removed from the channel.')
            .addFields(
                { name: '🔒 Closed By', value: `${user}`, inline: true },
                { name: '⏰ Closed At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '📁 Status', value: 'Archived - Messages Preserved', inline: true }
            )
            .setTimestamp();

        await interaction.reply({ embeds: [closingEmbed] });

        // Log the closure
        await logTeamAction(interaction.guild, 'Ticket Closed', `Team request ticket closed by ${user.tag}`, user, '#FF0000');

    } catch (error) {
        console.error('Error closing team ticket:', error);
        await interaction.reply({
            content: '❌ An error occurred while closing the ticket.',
            ephemeral: true
        });
    }
}

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        if (!interaction.isButton()) return;

        try {
            // Handle team request buttons
            if (interaction.customId.startsWith('team_request_')) {
                const requestType = interaction.customId.replace('team_request_', '');

                // Only allow leave, create, and help requests
                if (!['leave', 'create', 'help'].includes(requestType)) {
                    const errorEmbed = new EmbedBuilder()
                        .setColor('#747595')
                        .setTitle('❌ Request Not Available')
                        .setDescription('This request type is not currently available.')
                        .setTimestamp();

                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                await interaction.deferReply({ ephemeral: true });

                const ticketChannel = await createTeamTicket(interaction, requestType);

                const successEmbed = new EmbedBuilder()
                    .setColor('#747595')
                    .setTitle('✅ تم إنشاء التذكرة')
                    .setDescription(`تم إنشاء تذكرة طلبك: ${ticketChannel}`)
                    .setFooter({ text: 'الإدارة ستساعدك قريباً' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [successEmbed] });
            }
            
            // Handle ticket close buttons
            else if (interaction.customId.startsWith('close_team_ticket_')) {
                await closeTeamTicket(interaction);
            }

        } catch (error) {
            console.error('Error in team request handler:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing your request. Please try again later.')
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
